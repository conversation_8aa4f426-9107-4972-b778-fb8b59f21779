{"name": "wullup", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"upgrade:tamagui": "yarn upgrade @tamagui/config @tamagui/metro-plugin tamagui", "start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@animatereactnative/marquee": "^0.5.2", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-native-google-signin/google-signin": "^12.2.1", "@react-navigation/native": "^7.1.6", "@tamagui/config": "1.126.13", "@tamagui/linear-gradient": "1.126.13", "@tamagui/lucide-icons": "1.126.13", "@tamagui/metro-plugin": "1.126.13", "@tamagui/toast": "1.126.13", "@tanstack/react-query": "^5.77.1", "axios": "^1.9.0", "babel-preset-expo": "~13.0.0", "burnt": "^0.13.0", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-apple-authentication": "~7.2.4", "expo-application": "~6.1.4", "expo-audio": "~0.4.8", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-contacts": "~14.2.5", "expo-crypto": "~14.1.4", "expo-dev-client": "^5.1.8", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.6", "expo-notifications": "~0.31.2", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-tracking-transparency": "~5.2.4", "expo-web-browser": "~14.2.0", "lodash": "^4.17.21", "posthog-react-native": "^2.11.6", "react": "19.0.0", "react-dom": "19.0.0", "react-helmet-async": "~2.0.5", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "tamagui": "1.126.13", "typescript": "~5.8.3", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/react": "~19.0.10"}, "private": true, "packageManager": "yarn@4.0.2"}