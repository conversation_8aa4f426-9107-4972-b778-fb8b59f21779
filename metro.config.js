// Learn more https://docs.expo.io/guides/customizing-metro
/**
 * @type {import('expo/metro-config').MetroConfig}
 */
const { getDefaultConfig } = require('expo/metro-config')
const path = require('path')

const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
})

// Add resolver configuration to handle the path resolution issue
config.resolver.sourceExts.push('mjs')
config.resolver.resolverMainFields = ['react-native', 'browser', 'main']

// Add custom resolver to handle problematic paths
const originalResolver = config.resolver.resolveRequest
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Skip problematic FileStore resolution
  if (moduleName.includes('src/stores/FileStore')) {
    console.warn('Skipping problematic FileStore resolution:', moduleName)
    return null
  }

  // Use original resolver for everything else
  if (originalResolver) {
    return originalResolver(context, moduleName, platform)
  }

  return context.resolveRequest(context, moduleName, platform)
}

// add nice web support with optimizing compiler + CSS extraction
const { withTamagui } = require('@tamagui/metro-plugin')
module.exports = withTamagui(config, {
  components: ['tamagui'],
  config: './tamagui.config.ts',
  outputCSS: './tamagui-output.css',
})
