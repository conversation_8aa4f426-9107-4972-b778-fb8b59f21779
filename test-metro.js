// Simple test to check if Metro can start without the problematic import
console.log('Testing Metro configuration...');

// Try to require metro directly
try {
  const metro = require('metro');
  console.log('Metro loaded successfully');
} catch (error) {
  console.error('Metro loading failed:', error.message);
}

// Try to require metro-cache
try {
  const metroCache = require('metro-cache');
  console.log('Metro-cache loaded successfully');
} catch (error) {
  console.error('Metro-cache loading failed:', error.message);
}
