{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "wullup", "version": "1.0.0", "orientation": "portrait", "icon": "src/assets/images/icon.png", "scheme": "wullup", "userInterfaceStyle": "automatic", "splash": {"image": "src/assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "usesAppleSignIn": true, "bundleIdentifier": "com.wull.up", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["audio"]}, "buildNumber": "40", "entitlements": {"aps-environment": "production"}}, "android": {"package": "com.wull.up", "versionCode": 40, "adaptiveIcon": {"foregroundImage": "src/assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "com.google.android.gms.permission.AD_ID"]}, "web": {"bundler": "metro", "output": "static", "favicon": "src/assets/images/favicon.png"}, "plugins": [["expo-location", {"locationWhenInUsePermission": "Allow Wu<PERSON>up to use your location."}], "expo-router", "expo-font", "expo-apple-authentication", ["expo-audio", {"microphonePermission": "Allow <PERSON><PERSON><PERSON> to access your microphone."}], "expo-localization", "expo-secure-store", ["expo-image-picker", {"photosPermission": "The app requires access to your photos to allow you to upload and set a profile picture, as well as share images with your friends. This access is essential for personalizing your account and enhancing social interactions within the app."}], ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.************-ess4r5pcmhpo8i97tn75ljkinffdu85h"}], ["expo-tracking-transparency", {"userTrackingPermission": "The app uses analytics to analyze how you use Wullup, helping us understand which features you find most valuable and how we can enhance your experience. This data is crucial for improving app functionality."}], ["expo-build-properties", {"ios": {"deploymentTarget": "15.1"}, "android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24}}], "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "4bdb7c16-0258-4f9a-baed-454b657f2ee7"}}, "owner": "wullup"}}